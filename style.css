* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
    min-height: 100vh;
    color: #333;
}

.page {
    display: none;
    min-height: 100vh;
    padding: 20px;
}

.page.active {
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 登录页面样式 */
.login-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 40px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 400px;
    width: 100%;
}

.login-container h1 {
    color: #e91e63;
    margin-bottom: 30px;
    font-size: 2em;
}

.login-form input {
    width: 100%;
    padding: 15px;
    border: 2px solid #f8bbd9;
    border-radius: 10px;
    font-size: 16px;
    margin-bottom: 20px;
    text-align: center;
}

.login-form input:focus {
    outline: none;
    border-color: #e91e63;
}

.login-form button {
    width: 100%;
    padding: 15px;
    background: linear-gradient(45deg, #e91e63, #f06292);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 18px;
    cursor: pointer;
    transition: transform 0.2s;
}

.login-form button:hover {
    transform: translateY(-2px);
}

.hearts {
    margin-top: 20px;
    font-size: 1.5em;
    animation: pulse 2s infinite;
}

/* 主页面样式 */
.main-container {
    background: rgba(255, 255, 255, 0.95);
    padding: 30px;
    border-radius: 20px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    max-width: 600px;
    width: 100%;
    margin: 0 auto;
}

.main-container h1 {
    text-align: center;
    color: #e91e63;
    margin-bottom: 30px;
    font-size: 2em;
}

.prize-pool {
    background: linear-gradient(135deg, #fff 0%, #f8f9ff 100%);
    padding: 25px;
    margin: 20px 0;
    border-radius: 15px;
    border: 2px solid #f0f0f0;
    text-align: center;
}

.prize-pool h2 {
    color: #333;
    margin-bottom: 15px;
}

.prize-info {
    margin-bottom: 20px;
}

.probability {
    font-weight: bold;
    color: #e91e63;
    font-size: 1.2em;
}

.draw-btn {
    padding: 15px 40px;
    font-size: 18px;
    border: none;
    border-radius: 50px;
    cursor: pointer;
    transition: all 0.3s;
    position: relative;
    overflow: hidden;
}

.pool1-btn {
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
    color: white;
}

.pool2-btn {
    background: linear-gradient(45deg, #2196F3, #03DAC6);
    color: white;
}

.draw-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.draw-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
}

/* 神秘代码区域 */
.secret-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 25px;
    margin: 20px 0;
    border-radius: 15px;
    text-align: center;
}

.secret-section h3 {
    margin-bottom: 10px;
}

.hint {
    font-size: 14px;
    opacity: 0.9;
    margin-bottom: 20px;
}

.secret-form {
    display: flex;
    gap: 10px;
    max-width: 300px;
    margin: 0 auto;
}

.secret-form input {
    flex: 1;
    padding: 10px;
    border: none;
    border-radius: 5px;
    text-align: center;
}

.secret-form button {
    padding: 10px 20px;
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 5px;
    cursor: pointer;
}

/* 我的奖品 */
.my-prizes {
    margin-top: 30px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
}

.my-prizes h3 {
    text-align: center;
    color: #333;
    margin-bottom: 15px;
}

.prizes-list {
    text-align: center;
}

.prize-item {
    background: white;
    padding: 15px;
    margin: 10px 0;
    border-radius: 8px;
    border-left: 4px solid #e91e63;
}

.no-prizes {
    color: #666;
    font-style: italic;
}

/* 弹窗样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    animation: fadeIn 0.3s;
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 30px;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    text-align: center;
    animation: slideIn 0.3s;
}

.modal-content h2 {
    color: #e91e63;
    margin-bottom: 15px;
}

.modal-content button {
    margin-top: 20px;
    padding: 10px 30px;
    background: #e91e63;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
}

/* 加载动画 */
.loading {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.spinner {
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top: 4px solid white;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 0 auto 20px;
}

/* 动画 */
@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.1); }
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .login-container, .main-container {
        padding: 20px;
        margin: 10px;
    }
    
    .main-container h1 {
        font-size: 1.5em;
    }
    
    .prize-pool {
        padding: 20px;
    }
    
    .draw-btn {
        padding: 12px 30px;
        font-size: 16px;
    }
    
    .secret-form {
        flex-direction: column;
    }
    
    .secret-form button {
        margin-top: 10px;
    }
}

@media (max-width: 480px) {
    .page {
        padding: 10px;
    }
    
    .login-container, .main-container {
        padding: 15px;
    }
    
    .modal-content {
        margin: 20% auto;
        padding: 20px;
    }
}
