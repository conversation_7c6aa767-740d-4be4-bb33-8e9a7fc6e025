<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>项目测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 { color: #e91e63; }
        .status { padding: 10px; margin: 10px 0; border-radius: 5px; }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .file-list { background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0; }
        .file-item { margin: 5px 0; }
        .btn { 
            display: inline-block; 
            padding: 10px 20px; 
            background: #e91e63; 
            color: white; 
            text-decoration: none; 
            border-radius: 5px; 
            margin: 10px 5px 0 0;
        }
        .btn:hover { background: #c2185b; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎁 宝宝抽奖系统 - 项目测试</h1>
        
        <div class="status success">
            ✅ 项目文件创建完成！
        </div>
        
        <div class="status info">
            📋 项目包含以下文件：
        </div>
        
        <div class="file-list">
            <div class="file-item">📄 <strong>index.html</strong> - 主页面 (登录 + 抽奖界面)</div>
            <div class="file-item">🎨 <strong>style.css</strong> - 响应式样式文件</div>
            <div class="file-item">⚡ <strong>script.js</strong> - 前端交互逻辑</div>
            <div class="file-item">🔧 <strong>api.php</strong> - 后端API接口</div>
            <div class="file-item">💾 <strong>data.json</strong> - 数据存储文件</div>
            <div class="file-item">📖 <strong>README.md</strong> - 使用说明文档</div>
        </div>
        
        <h2>🚀 部署说明</h2>
        <div class="status info">
            <strong>要运行这个抽奖系统，你需要：</strong><br>
            1. 一个支持PHP的Web服务器 (如XAMPP、WAMP、MAMP等)<br>
            2. 将所有文件放到Web服务器的根目录<br>
            3. 确保data.json文件有读写权限<br>
            4. 通过浏览器访问index.html
        </div>
        
        <h2>🎯 功能特点</h2>
        <ul>
            <li>✨ 只有输入"卓卓"才能进入系统</li>
            <li>🎁 奖池一：拼豆补豆券 (100%概率)</li>
            <li>✈️ 奖池二：旅行券 (需要神秘代码提升概率)</li>
            <li>🔮 神秘代码："龙哥哥" (将旅行券概率提升到100%)</li>
            <li>📱 完美适配手机、平板、电脑</li>
            <li>🏆 自动记录所有抽中的奖品</li>
        </ul>
        
        <h2>🎮 使用流程</h2>
        <ol>
            <li>输入"卓卓"登录系统</li>
            <li>先抽奖池一，100%获得拼豆补豆券</li>
            <li>抽奖池二，初始概率为0%</li>
            <li>给龙哥哥发"龙哥哥"获取神秘代码</li>
            <li>输入神秘代码"龙哥哥"</li>
            <li>再次抽奖池二，100%获得旅行券</li>
        </ol>
        
        <a href="index.html" class="btn">🎁 开始抽奖</a>
        <a href="README.md" class="btn">📖 查看文档</a>
    </div>
</body>
</html>
