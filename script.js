// 全局变量
let currentUser = '';
let userPrizes = [];
let secretCodeUsed = false;

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    loadUserData();
});

// 登录功能
function login() {
    const input = document.getElementById('loginInput');
    const username = input.value.trim();
    
    if (username === '卓卓') {
        currentUser = username;
        document.getElementById('loginPage').classList.remove('active');
        document.getElementById('mainPage').classList.add('active');
        loadUserData();
    } else {
        alert('只有卓卓才能进入哦~ 💕');
        input.value = '';
        input.focus();
    }
}

// 监听回车键登录
document.addEventListener('keypress', function(e) {
    if (e.key === 'Enter') {
        const loginPage = document.getElementById('loginPage');
        const secretInput = document.getElementById('secretCodeInput');
        
        if (loginPage.classList.contains('active')) {
            login();
        } else if (document.activeElement === secretInput) {
            submitSecretCode();
        }
    }
});

// 加载用户数据
async function loadUserData() {
    try {
        const response = await fetch('api.php?action=getUserData');
        const data = await response.json();

        if (data.success) {
            userPrizes = data.data.prizes_won || [];
            secretCodeUsed = data.data.secret_code_used || false;

            // 显示用户奖品
            displayUserPrizes();

            // 检查按钮状态
            updateButtonStates();
        }
    } catch (error) {
        console.error('加载用户数据失败:', error);
    }
}

// 更新按钮状态
function updateButtonStates() {
    const pool1Btn = document.querySelector('.pool1-btn');
    const pool2Btn = document.querySelector('.pool2-btn');

    // 检查是否已经抽中过奖池1
    const hasPool1Prize = userPrizes.some(prize => prize.pool === 1);
    if (hasPool1Prize) {
        pool1Btn.disabled = true;
        pool1Btn.innerHTML = '<span>已抽中</span>';
        pool1Btn.style.opacity = '0.6';
    }

    // 检查是否已经抽中过奖池2
    const hasPool2Prize = userPrizes.some(prize => prize.pool === 2);
    if (hasPool2Prize) {
        pool2Btn.disabled = true;
        pool2Btn.innerHTML = '<span>已抽中</span>';
        pool2Btn.style.opacity = '0.6';
    }
}

// 抽奖功能
async function drawPrize(poolId) {
    const button = event.target.closest('.draw-btn');
    button.disabled = true;
    
    // 显示加载动画
    showLoadingModal();
    
    try {
        const response = await fetch('api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'drawPrize',
                poolId: poolId
            })
        });
        
        const result = await response.json();
        
        // 延迟显示结果，增加悬念
        setTimeout(() => {
            hideLoadingModal();

            if (result.success) {
                if (result.data && result.data.won) {
                    showResult('🎉 恭喜宝贝！', result.message);

                    // 如果抽中旅行券，显示提示
                    if (result.data.prize === '旅行券') {
                        setTimeout(() => {
                            showResult('💌 特别提示', '快去给龙哥哥发"龙哥哥"获取神秘代码吧！');
                            document.getElementById('secretCodeSection').style.display = 'block';
                        }, 2000);
                    }

                    // 重新加载用户数据
                    loadUserData();
                } else {
                    // 没有抽中
                    showResult('😢 很遗憾', result.message);

                    // 如果是奖池2第一次抽奖，显示神秘代码输入区域
                    if (result.data && result.data.show_secret_hint) {
                        setTimeout(() => {
                            document.getElementById('secretCodeSection').style.display = 'block';
                        }, 2000);
                    }
                }
            } else {
                showResult('❌ 出错了', result.message || '抽奖失败，请重试');
            }

            button.disabled = false;
        }, 2000);
        
    } catch (error) {
        hideLoadingModal();
        showResult('❌ 网络错误', '请检查网络连接后重试');
        button.disabled = false;
    }
}

// 提交神秘代码
async function submitSecretCode() {
    const input = document.getElementById('secretCodeInput');
    const code = input.value.trim();
    
    if (!code) {
        alert('请输入神秘代码');
        return;
    }
    
    try {
        const response = await fetch('api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'submitSecretCode',
                code: code
            })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showResult('✨ 神秘代码正确！', '现在可以再次抽取旅行券了，这次一定能抽中！');
            document.getElementById('secretCodeSection').style.display = 'none';
            secretCodeUsed = true;

            // 重新启用奖池2按钮
            const pool2Btn = document.querySelector('.pool2-btn');
            pool2Btn.disabled = false;
            pool2Btn.innerHTML = '<span>抽奖</span>';
            pool2Btn.style.opacity = '1';
        } else {
            showResult('❌ 代码错误', '神秘代码不正确，请重新输入');
        }
        
        input.value = '';
    } catch (error) {
        showResult('❌ 网络错误', '请检查网络连接后重试');
    }
}

// 显示用户奖品
function displayUserPrizes() {
    const prizesList = document.getElementById('prizesList');

    if (userPrizes.length === 0) {
        prizesList.innerHTML = '<p class="no-prizes">还没有抽中任何奖品哦~</p>';
    } else {
        const prizesHtml = userPrizes.map(prize => {
            let useButton = '';
            if (prize.name === '拼豆-补豆券') {
                useButton = `<button class="use-btn" onclick="usePrize('${prize.name}')" id="useBtn-${prize.pool}">使用券</button>`;
            }

            return `
                <div class="prize-item">
                    <div class="prize-info">
                        <strong>${prize.name}</strong>
                        <br>
                        <small>获得时间: ${new Date(prize.time).toLocaleString()}</small>
                    </div>
                    ${useButton}
                </div>
            `;
        }).join('');

        prizesList.innerHTML = prizesHtml;

        // 更新使用按钮状态
        updateUseButtonState();
    }
}

// 显示结果弹窗
function showResult(title, message) {
    document.getElementById('resultTitle').textContent = title;
    document.getElementById('resultMessage').textContent = message;
    document.getElementById('resultModal').style.display = 'block';
}

// 关闭弹窗
function closeModal() {
    document.getElementById('resultModal').style.display = 'none';
}

// 显示加载动画
function showLoadingModal() {
    document.getElementById('loadingModal').style.display = 'block';
}

// 隐藏加载动画
function hideLoadingModal() {
    document.getElementById('loadingModal').style.display = 'none';
}

// 使用奖品功能
async function usePrize(prizeName) {
    if (prizeName !== '拼豆-补豆券') {
        return;
    }

    try {
        const response = await fetch('api.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                action: 'usePrize',
                prizeName: prizeName
            })
        });

        const result = await response.json();

        if (result.success) {
            showResult('✨ 使用成功！', result.message);
            // 重新加载用户数据以更新按钮状态
            loadUserData();
        } else {
            showResult('❌ 使用失败', result.message);
        }
    } catch (error) {
        showResult('❌ 网络错误', '请检查网络连接后重试');
    }
}

// 更新使用按钮状态
async function updateUseButtonState() {
    try {
        const response = await fetch('api.php?action=getPrizeUsage&prizeName=拼豆-补豆券');
        const data = await response.json();

        if (data.success) {
            const useBtn = document.getElementById('useBtn-1');
            if (useBtn) {
                const usedCount = data.data.used_count || 0;
                const maxUses = data.data.max_uses || 2;
                const remainingUses = maxUses - usedCount;

                if (remainingUses > 0) {
                    useBtn.textContent = `使用券 (${remainingUses}/${maxUses})`;
                    useBtn.disabled = false;
                } else {
                    useBtn.textContent = '已用尽';
                    useBtn.disabled = true;
                    useBtn.style.opacity = '0.6';
                }
            }
        }
    } catch (error) {
        console.error('获取奖品使用状态失败:', error);
    }
}

// 点击弹窗外部关闭
window.onclick = function(event) {
    const resultModal = document.getElementById('resultModal');
    const loadingModal = document.getElementById('loadingModal');

    if (event.target === resultModal) {
        closeModal();
    }

    // 加载动画不允许点击外部关闭
}
