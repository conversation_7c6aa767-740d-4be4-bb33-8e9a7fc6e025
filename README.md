# 宝宝专属抽奖系统 💕

一个专为女朋友制作的浪漫抽奖页面，支持手机、平板等多设备访问。

## 功能特点

### 🔐 登录验证
- 只有输入"卓卓"才能进入抽奖系统
- 粉色浪漫主题设计

### 🎁 双奖池系统
- **奖池一：拼豆补豆券** - 100%中奖概率
- **奖池二：旅行券** - 初始0%概率，使用神秘代码后变为100%

### 🔮 神秘代码机制
- 抽中旅行券后会提示联系"龙哥哥"
- 输入神秘代码"龙哥哥"可将旅行券概率提升至100%
- 增加互动趣味性

### 📱 响应式设计
- 完美适配手机、平板、桌面设备
- 流畅的动画效果和用户体验

### 🏆 奖品记录
- 自动记录所有抽中的奖品
- 显示获奖时间和奖品详情

## 技术架构

- **前端**: HTML5 + CSS3 + JavaScript (原生)
- **后端**: PHP
- **数据存储**: JSON文件 (无需数据库)
- **设计**: 响应式布局，支持多设备

## 部署说明

### 环境要求
- PHP 7.0 或更高版本
- Web服务器 (Apache/Nginx)

### 部署步骤
1. 将所有文件上传到Web服务器目录
2. 确保PHP有读写权限 (用于data.json文件)
3. 访问 `index.html` 开始使用

### 文件结构
```
宝宝抽奖/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 前端逻辑
├── api.php         # 后端API
├── data.json       # 数据存储
└── README.md       # 说明文档
```

## 使用流程

1. **登录**: 输入"卓卓"进入系统
2. **抽奖池一**: 点击抽奖按钮，100%获得拼豆补豆券
3. **抽奖池二**: 初次抽奖概率为0%
4. **获取神秘代码**: 给龙哥哥发"龙哥哥"获取神秘代码
5. **输入神秘代码**: 输入"龙哥哥"提升概率到100%
6. **再次抽奖**: 100%概率获得旅行券
7. **查看奖品**: 在"我的奖品"区域查看所有获得的奖品

## 自定义配置

可以通过修改 `data.json` 文件来自定义：
- 登录密码
- 奖品名称和描述
- 神秘代码
- 初始概率设置

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 注意事项

- 确保服务器支持PHP
- data.json文件需要有写入权限
- 建议在HTTPS环境下使用以获得最佳体验

---

💝 专为卓卓制作，满满的爱意！
