<?php
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// 处理预检请求
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

// 数据文件路径
$dataFile = 'data.json';

// 读取数据
function readData() {
    global $dataFile;
    if (!file_exists($dataFile)) {
        // 如果文件不存在，创建默认数据
        $defaultData = [
            'user_data' => [
                'prizes_won' => [],
                'secret_code_used' => false,
                'travel_voucher_probability' => 0
            ],
            'prizes' => [
                'pool1' => [
                    'name' => '拼豆-补豆券',
                    'probability' => 100,
                    'description' => '恭喜宝贝抽中拼豆补豆券！'
                ],
                'pool2' => [
                    'name' => '旅行券',
                    'probability' => 0,
                    'description' => '恭喜宝贝抽中旅行券！快去找龙哥哥兑换吧！'
                ]
            ],
            'secret_code' => '龙哥哥',
            'settings' => [
                'login_password' => '卓卓'
            ]
        ];
        writeData($defaultData);
        return $defaultData;
    }
    
    $content = file_get_contents($dataFile);
    return json_decode($content, true);
}

// 写入数据
function writeData($data) {
    global $dataFile;
    $jsonData = json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE);
    file_put_contents($dataFile, $jsonData, LOCK_EX);
}

// 响应函数
function response($success, $message = '', $data = null) {
    echo json_encode([
        'success' => $success,
        'message' => $message,
        'data' => $data
    ], JSON_UNESCAPED_UNICODE);
    exit;
}

// 抽奖逻辑
function drawPrize($poolId) {
    $data = readData();

    // 检查是否已经抽中过该奖池的奖品
    $alreadyWon = false;
    foreach ($data['user_data']['prizes_won'] as $prize) {
        if ($prize['pool'] == $poolId) {
            $alreadyWon = true;
            break;
        }
    }

    if ($alreadyWon) {
        response(false, '你已经抽中过这个奖池的奖品了哦~');
        return;
    }

    if ($poolId == 1) {
        // 奖池1：拼豆补豆券，100%中奖
        $prize = $data['prizes']['pool1'];
        $won = true;
    } else if ($poolId == 2) {
        // 奖池2：旅行券，概率根据是否使用神秘代码决定
        $prize = $data['prizes']['pool2'];
        $probability = $data['user_data']['travel_voucher_probability'];

        // 如果概率为0，直接不中奖并提示联系龙哥哥
        if ($probability == 0) {
            response(true, '很遗憾没有抽中！快去给龙哥哥发"龙哥哥"获取神秘代码吧！', [
                'won' => false,
                'show_secret_hint' => true
            ]);
            return;
        }

        // 如果使用了神秘代码，100%中奖
        $won = $probability >= 100;
    } else {
        response(false, '无效的奖池ID');
    }

    if ($won) {
        // 记录中奖信息
        $prizeRecord = [
            'name' => $prize['name'],
            'time' => date('Y-m-d H:i:s'),
            'pool' => $poolId
        ];

        $data['user_data']['prizes_won'][] = $prizeRecord;
        writeData($data);

        response(true, $prize['description'], [
            'won' => true,
            'prize' => $prize['name']
        ]);
    } else {
        response(true, '很遗憾，这次没有抽中，再试试吧！', [
            'won' => false
        ]);
    }
}

// 提交神秘代码
function submitSecretCode($code) {
    $data = readData();
    
    if ($code === $data['secret_code']) {
        // 神秘代码正确，提升旅行券概率到100%
        $data['user_data']['secret_code_used'] = true;
        $data['user_data']['travel_voucher_probability'] = 100;
        writeData($data);
        
        response(true, '神秘代码正确！旅行券抽中概率已提升到100%！');
    } else {
        response(false, '神秘代码不正确');
    }
}

// 获取用户数据
function getUserData() {
    $data = readData();
    response(true, '获取成功', $data['user_data']);
}

// 主要处理逻辑
try {
    if ($_SERVER['REQUEST_METHOD'] === 'GET') {
        $action = $_GET['action'] ?? '';
        
        switch ($action) {
            case 'getUserData':
                getUserData();
                break;
            default:
                response(false, '无效的GET请求');
        }
    } else if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $input = json_decode(file_get_contents('php://input'), true);
        $action = $input['action'] ?? '';
        
        switch ($action) {
            case 'drawPrize':
                $poolId = $input['poolId'] ?? 0;
                drawPrize($poolId);
                break;
                
            case 'submitSecretCode':
                $code = $input['code'] ?? '';
                submitSecretCode($code);
                break;
                
            default:
                response(false, '无效的POST请求');
        }
    } else {
        response(false, '不支持的请求方法');
    }
} catch (Exception $e) {
    response(false, '服务器错误: ' . $e->getMessage());
}
?>
